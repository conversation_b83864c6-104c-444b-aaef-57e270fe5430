import datetime
from itertools import groupby
from itertools import tee
import pandas as pd

df = pd.read_csv('X:\\track_report\\driving_time\\drivingtime.csv')

total_driving_time = 0
driver = {}
for i, j in groupby(df.iterrows(), key=lambda row: (row[1]['speed'] if row[1]['speed'] == 0 else 1)):
    itorMax, itorMin = tee(j, 2)
    # str to datetime
    maxItem = max(itorMax, key=lambda row: row[1]['gps_time'])
    minItem = min(itorMin, key=lambda row: row[1]['gps_time'])
    maxTimeStr = maxItem[1]['gps_time']
    minTimeStr = minItem[1]['gps_time']
    driverId = maxItem[1]['id_card']
    if driverId not in driver:
        driver[driverId] = 0
    maxTime = datetime.datetime.strptime(maxTimeStr, "%Y-%m-%d %H:%M:%S.000")
    minTime = datetime.datetime.strptime(minTimeStr, "%Y-%m-%d %H:%M:%S.000")

    if i == 1:
        print(f"{driverId} 行驶时间段：开始时间：{minTimeStr}，结束时间：{maxTimeStr}，持续时长：{maxTime - minTime}")
        total_driving_time += ((maxTime - minTime).seconds)
        driver[driverId] += ((maxTime - minTime).seconds)
    else:
        total_stop_time += ((maxTime - minTime).seconds)

    if total_driving_time > 2*60*60 and total_stop_time < 1200:
        print(f"{driverId} 驾驶超时 \t")
        total_stop_time = 0
        total_driving_time = 0

    if total_stop_time > 1200:
        total_driving_time = 0
        total_stop_time = 0
        print(f"{driverId} 停车时长足够了，可以清除驾驶时间")

print(f"""车辆驾驶时间：{total_driving_time / 3600:.2f}小时""")
for k, v in driver.items():
    print(f"{k} 驾驶时间：{v / 3600:.2f}小时")
